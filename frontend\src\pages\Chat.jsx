import React, { useState, useEffect } from 'react';
import { createClient } from "@supabase/supabase-js";
import Sidebar from "../components/Sidebar";
import ChatInterface from "../components/ChatInterface";
import History from "../components/History";
import "./Chat.css";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const Chat = () => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(true);
  const [session, setSession] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const toggleHistory = () => setIsHistoryOpen((prev) => !prev);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <div className="chat-app">
      {/* Mobile Navigation */}
      <div className="mobile-nav d-md-none">
        <div className="nav-brand">
          <h1 className="brand-text">Recallo</h1>
        </div>
        <div className="nav-actions">
          <button
            className="sidebar-toggle"
            onClick={toggleHistory}
            aria-label="Toggle history"
          >
            <i className="fas fa-history"></i>
          </button>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="desktop-layout d-none d-md-flex">
        {/* Sidebar */}
        <div className="sidebar-section">
          <Sidebar />
        </div>

        {/* Chat Section */}
        <div className="chat-section">
          <ChatInterface session={session} />
        </div>

        {/* History Section */}
        {isHistoryOpen && (
          <div className="history-section">
            <History isLoggedIn={isLoggedIn} session={session} />
          </div>
        )}
      </div>

      {/* Mobile Layout */}
      <div className="mobile-layout d-md-none">
        {isHistoryOpen ? (
          <div className="mobile-history">
            <History isLoggedIn={isLoggedIn} session={session} />
          </div>
        ) : (
          <div className="mobile-chat">
            <ChatInterface session={session} />
          </div>
        )}
      </div>

      {/* Mobile Sidebar Overlay */}
      {isHistoryOpen && (
        <div className="mobile-sidebar-overlay d-md-none" onClick={toggleHistory}>
          <div className="mobile-sidebar" onClick={(e) => e.stopPropagation()}>
            <Sidebar />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;



// check login status dummy 
// localStorage.setItem("userToken", "demo_token");
// localStorage.removeItem("userToken");
