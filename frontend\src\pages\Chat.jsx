import React, { useState, useEffect } from 'react';
import { createClient } from "@supabase/supabase-js";
import Sidebar from "../components/Sidebar";
import ChatInterface from "../components/ChatInterface";
import History from "../components/History";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const Chat = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [session, setSession] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedChatSession, setSelectedChatSession] = useState(null);

  const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

  const handleChatSelect = (chatSession) => {
    setSelectedChatSession(chatSession);
    // You can add logic here to display the selected chat session
    console.log('Selected chat session:', chatSession);
  };

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <div className="container-fluid p-0 chat">
      <div className="row">
        <div className="col-md-2">
          {/* is logged in for dummy check */}
          <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} isLoggedIn={isLoggedIn} />
          {/* <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar}/> */}
        </div>
        <div className="col-md-8">
          <ChatInterface session={session} />
        </div>
        <div className="col-md-2">
          {isSidebarOpen && (
            <History
              isLoggedIn={isLoggedIn}
              session={session}
              onChatSelect={handleChatSelect}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Chat;



// check login status dummy 
// localStorage.setItem("userToken", "demo_token");
// localStorage.removeItem("userToken");
