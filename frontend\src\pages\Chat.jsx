import React, { useState, useEffect } from 'react';
import { createClient } from "@supabase/supabase-js";
import Sidebar from "../components/Sidebar";
import ChatInterface from "../components/ChatInterface";
import History from "../components/History";
import "./Chat.css";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const Chat = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [session, setSession] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedChatSession, setSelectedChatSession] = useState(null);
  const [currentView, setCurrentView] = useState('chat'); // 'chat' or 'history'

  const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

  const handleChatSelect = (chatSession) => {
    setSelectedChatSession(chatSession);
    setCurrentView('chat'); // Switch back to chat view when session is selected
    console.log('Selected chat session:', chatSession);
  };

  const showHistory = () => setCurrentView('history');
  const showChat = () => setCurrentView('chat');

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <div className="chat-app">
      {/* Mobile Navigation */}
      <div className="mobile-nav d-md-none">
        <button
          className={`nav-btn ${currentView === 'chat' ? 'active' : ''}`}
          onClick={showChat}
        >
          <i className="fas fa-comments"></i>
          Chat
        </button>
        <button
          className={`nav-btn ${currentView === 'history' ? 'active' : ''}`}
          onClick={showHistory}
        >
          <i className="fas fa-history"></i>
          History
        </button>
        <button className="sidebar-toggle" onClick={toggleSidebar} aria-label="Toggle sidebar">
          <i className="fas fa-bars"></i>
        </button>
      </div>

      {/* Desktop Layout */}
      <div className="desktop-layout d-none d-md-flex">
        <div className="sidebar-section">
          <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} isLoggedIn={isLoggedIn} />
        </div>
        <div className="chat-section">
          <ChatInterface session={session} />
        </div>
        <div className="history-section">
          <History
            isLoggedIn={isLoggedIn}
            session={session}
            onChatSelect={handleChatSelect}
          />
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="mobile-layout d-md-none">
        {currentView === 'chat' ? (
          <div className="mobile-chat">
            <ChatInterface session={session} />
          </div>
        ) : (
          <div className="mobile-history">
            <History
              isLoggedIn={isLoggedIn}
              session={session}
              onChatSelect={handleChatSelect}
            />
          </div>
        )}
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div className="mobile-sidebar-overlay d-md-none" onClick={toggleSidebar}>
          <div className="mobile-sidebar" onClick={(e) => e.stopPropagation()}>
            <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} isLoggedIn={isLoggedIn} />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;



// check login status dummy 
// localStorage.setItem("userToken", "demo_token");
// localStorage.removeItem("userToken");
