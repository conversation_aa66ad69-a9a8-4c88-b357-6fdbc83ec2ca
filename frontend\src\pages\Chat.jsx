import React, { useState, useEffect } from 'react';
import { createClient } from "@supabase/supabase-js";
import Sidebar from "../components/Sidebar";
import ChatInterface from "../components/ChatInterface";
import History from "../components/History";
import "./Chat.css";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const Chat = () => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(true);
  const [session, setSession] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const toggleHistory = () => setIsHistoryOpen((prev) => !prev);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <div className="chat-app">
      {/* ChatGPT-style Layout */}
      <div className="chatgpt-layout">
        {/* Left Sidebar */}
        <div className="left-sidebar">
          <Sidebar />
        </div>

        {/* Main Chat Area */}
        <div className="main-chat-area">
          {/* Top Bar */}
          <div className="chat-top-bar">
            <button
              className="history-toggle-btn"
              onClick={toggleHistory}
              aria-label="Toggle history sidebar"
            >
              <i className="fas fa-bars"></i>
            </button>

            <div className="chat-title">
              Recallo Chat
            </div>

            <button
              className="new-chat-btn"
              onClick={() => window.location.reload()}
              aria-label="Start new chat"
            >
              <i className="fas fa-plus"></i>
              New Chat
            </button>
          </div>

          {/* Chat Interface */}
          <div className="chat-content">
            <ChatInterface session={session} />
          </div>
        </div>

        {/* History Sidebar */}
        {isHistoryOpen && (
          <div className="history-sidebar">
            <History isLoggedIn={isLoggedIn} session={session} />
          </div>
        )}
      </div>

      {/* Mobile Sidebar Overlay */}
      {isHistoryOpen && (
        <div className="mobile-history-overlay d-md-none" onClick={toggleHistory}>
          <div className="mobile-history-sidebar" onClick={(e) => e.stopPropagation()}>
            <History isLoggedIn={isLoggedIn} session={session} />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;



// check login status dummy 
// localStorage.setItem("userToken", "demo_token");
// localStorage.removeItem("userToken");
