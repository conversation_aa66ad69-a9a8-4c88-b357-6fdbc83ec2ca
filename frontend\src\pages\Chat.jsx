import React, { useState, useEffect } from 'react';
import { createClient } from "@supabase/supabase-js";
import Sidebar from "../components/Sidebar";
import ChatInterface from "../components/ChatInterface";
import History from "../components/History";
import "./Chat.css";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const Chat = () => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(true);
  const [session, setSession] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [currentMessages, setCurrentMessages] = useState([]);

  const toggleHistory = () => setIsHistoryOpen((prev) => !prev);

  const handleConversationSelect = (conversation) => {
    setSelectedConversation(conversation);
    // Load the conversation messages into the chat interface
    setCurrentMessages(conversation.messages || []);
    console.log('Selected conversation:', conversation);
  };

  const handleNewChat = () => {
    setSelectedConversation(null);
    setCurrentMessages([]);
  };

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsLoggedIn(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <div className="chat-app">
      {/* ChatGPT-style Layout */}
      <div className="chatgpt-layout">
        {/* History Sidebar */}
        {isHistoryOpen && (
          <div className="history-sidebar">
            <History
              isLoggedIn={isLoggedIn}
              session={session}
              onChatSelect={handleConversationSelect}
              selectedConversationId={selectedConversation?.id}
            />
          </div>
        )}

        {/* Main Chat Area */}
        <div className="main-chat-area">
          {/* Top Bar */}
          <div className="chat-top-bar">
            <button
              className="history-toggle-btn"
              onClick={toggleHistory}
              aria-label="Toggle history sidebar"
            >
              <i className="fas fa-bars"></i>
            </button>

            <div className="chat-title">
              {selectedConversation ? selectedConversation.title : 'New Chat'}
            </div>

            <button
              className="new-chat-btn"
              onClick={handleNewChat}
              aria-label="Start new chat"
            >
              <i className="fas fa-plus"></i>
              New Chat
            </button>
          </div>

          {/* Chat Interface */}
          <div className="chat-content">
            <ChatInterface
              session={session}
              selectedConversation={selectedConversation}
              currentMessages={currentMessages}
              onNewMessage={(newMessages) => {
                // Add new messages to current messages and update conversation
                setCurrentMessages(prev => [...prev, ...newMessages]);

                // If we have a selected conversation, we might want to update it
                // For now, we'll let the history component refresh on next load
                console.log('New messages added:', newMessages);
              }}
            />
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isHistoryOpen && (
        <div className="mobile-history-overlay d-md-none" onClick={toggleHistory}>
          <div className="mobile-history-sidebar" onClick={(e) => e.stopPropagation()}>
            <History
              isLoggedIn={isLoggedIn}
              session={session}
              onChatSelect={handleConversationSelect}
              selectedConversationId={selectedConversation?.id}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;



// check login status dummy 
// localStorage.setItem("userToken", "demo_token");
// localStorage.removeItem("userToken");
