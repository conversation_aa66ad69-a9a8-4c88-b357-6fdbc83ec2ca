import React, { useEffect, useState } from "react";
import { fetchChatHistory } from "../../../backend/services/historyService";
import "./History.css";

const History = ({ isLoggedIn, session, onChatSelect }) => {
  const [chatSessions, setChatSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredSessions, setFilteredSessions] = useState([]);

  useEffect(() => {
    async function loadHistory() {
      if (isLoggedIn && session?.user?.id) {
        setLoading(true);
        setError(null);
        try {
          const records = await fetchChatHistory(session.user.id);
          // Group messages into chat sessions
          const sessions = groupMessagesIntoSessions(records);
          setChatSessions(sessions);
          setFilteredSessions(sessions);
        } catch (error) {
          console.error("Failed to load chat history:", error);
          setError("Failed to load chat history. Please try again.");
        } finally {
          setLoading(false);
        }
      } else {
        setChatSessions([]);
        setFilteredSessions([]);
      }
    }
    loadHistory();
  }, [isLoggedIn, session]);

  // Group messages into chat sessions based on conversation flow
  const groupMessagesIntoSessions = (messages) => {
    if (!messages || messages.length === 0) return [];

    const sessions = [];
    let currentSession = null;

    // Sort messages by timestamp
    const sortedMessages = [...messages].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    for (let i = 0; i < sortedMessages.length; i++) {
      const message = sortedMessages[i];

      // Start a new session if this is a user message and we don't have a current session
      // or if there's a significant time gap (more than 30 minutes)
      if (message.sender === 'user') {
        const timeDiff = currentSession ?
          (new Date(message.timestamp) - new Date(currentSession.lastMessage.timestamp)) / (1000 * 60) :
          Infinity;

        if (!currentSession || timeDiff > 30) {
          // Save previous session if exists
          if (currentSession) {
            sessions.push(currentSession);
          }

          // Start new session
          currentSession = {
            id: `session_${message.id}`,
            title: message.text.substring(0, 50) + (message.text.length > 50 ? '...' : ''),
            preview: message.text.substring(0, 100) + (message.text.length > 100 ? '...' : ''),
            startTime: message.timestamp,
            lastMessage: message,
            messages: [message],
            messageCount: 1
          };
        } else {
          // Add to current session
          currentSession.messages.push(message);
          currentSession.lastMessage = message;
          currentSession.messageCount++;
        }
      } else if (currentSession) {
        // Add bot response to current session
        currentSession.messages.push(message);
        currentSession.lastMessage = message;
        currentSession.messageCount++;
      }
    }

    // Add the last session
    if (currentSession) {
      sessions.push(currentSession);
    }

    // Sort sessions by most recent first
    return sessions.sort((a, b) => new Date(b.lastMessage.timestamp) - new Date(a.lastMessage.timestamp));
  };

  // Filter sessions based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredSessions(chatSessions);
    } else {
      const filtered = chatSessions.filter(session =>
        session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.preview.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSessions(filtered);
    }
  }, [searchTerm, chatSessions]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const handleSessionClick = (session) => {
    if (onChatSelect) {
      onChatSelect(session);
    }
  };

  return (
    <div className="history-container">
      <div className="history-header">
        <div className="header-content">
          <h2 className="history-title">
            <i className="fas fa-history"></i>
            Chat History
          </h2>
          <p className="history-subtitle">Your conversation sessions with Recallo</p>
        </div>

        {isLoggedIn && chatSessions.length > 0 && (
          <div className="search-container">
            <div className="search-input-wrapper">
              <i className="fas fa-search search-icon"></i>
              <input
                type="text"
                className="search-input"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="clear-search"
                  onClick={() => setSearchTerm("")}
                  aria-label="Clear search"
                >
                  <i className="fas fa-times"></i>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="history-content">
        {!isLoggedIn ? (
          <div className="auth-prompt">
            <div className="auth-icon">
              <i className="fas fa-lock"></i>
            </div>
            <h3>Sign In Required</h3>
            <p>Please sign in to access your personalized chat history and continue your learning journey.</p>
            <div className="auth-features">
              <div className="feature-item">
                <i className="fas fa-save"></i>
                <span>Save conversations</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-search"></i>
                <span>Search history</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-sync"></i>
                <span>Sync across devices</span>
              </div>
            </div>
          </div>
        ) : loading ? (
          <div className="loading-container">
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
            <p className="loading-text">Loading your conversations...</p>
          </div>
        ) : error ? (
          <div className="error-container">
            <div className="error-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <h3>Oops! Something went wrong</h3>
            <p>{error}</p>
            <button
              className="retry-button"
              onClick={() => window.location.reload()}
            >
              <i className="fas fa-redo"></i>
              Try Again
            </button>
          </div>
        ) : filteredSessions.length === 0 ? (
          searchTerm ? (
            <div className="no-results">
              <div className="no-results-icon">
                <i className="fas fa-search"></i>
              </div>
              <h3>No matches found</h3>
              <p>No conversations match "{searchTerm}". Try a different search term.</p>
              <button
                className="clear-search-button"
                onClick={() => setSearchTerm("")}
              >
                Clear Search
              </button>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-icon">
                <i className="fas fa-comments"></i>
              </div>
              <h3>No conversations yet</h3>
              <p>Start chatting with Recallo to build your learning history!</p>
              <div className="empty-features">
                <div className="feature-tip">
                  <i className="fas fa-lightbulb"></i>
                  <span>Ask questions about any topic</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-brain"></i>
                  <span>Get personalized learning assistance</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-chart-line"></i>
                  <span>Track your learning progress</span>
                </div>
              </div>
            </div>
          )
        ) : (
          <div className="chat-sessions-list">
            <div className="results-info">
              {searchTerm && (
                <p className="search-results">
                  Found {filteredSessions.length} conversation{filteredSessions.length !== 1 ? 's' : ''}
                  matching "{searchTerm}"
                </p>
              )}
            </div>

            {filteredSessions.map((session, index) => (
              <div
                key={session.id}
                className="session-card"
                style={{ animationDelay: `${index * 0.1}s` }}
                onClick={() => handleSessionClick(session)}
              >
                <div className="session-header">
                  <div className="session-icon">
                    <i className="fas fa-comments"></i>
                  </div>
                  <div className="session-info">
                    <h4 className="session-title">{session.title}</h4>
                    <p className="session-preview">{session.preview}</p>
                  </div>
                  <div className="session-meta">
                    <span className="session-time">{formatTime(session.lastMessage.timestamp)}</span>
                    <span className="message-count">
                      <i className="fas fa-comment"></i>
                      {session.messageCount}
                    </span>
                  </div>
                </div>

                {searchTerm && (session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  session.preview.toLowerCase().includes(searchTerm.toLowerCase())) && (
                  <div className="search-highlight-indicator">
                    <i className="fas fa-search"></i>
                    <span>Match found</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
