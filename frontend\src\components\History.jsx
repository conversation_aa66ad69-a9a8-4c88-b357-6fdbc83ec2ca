import React, { useEffect, useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { fetchChatHistory } from "../../../backend/services/historyService";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

const History = ({ isLoggedIn }) => {
  const [history, setHistory] = useState([]);
  const session = supabase.auth.getSession
    ? supabase.auth.getSession().then(({ data }) => data.session)
    : null;

  useEffect(() => {
    async function loadHistory() {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (isLoggedIn && session?.user?.id) {
        const records = await fetchChatHistory(session.user.id);
        setHistory(records);
      }
    }
    loadHistory();
  }, [isLoggedIn]);

  return (
    <div className="history container mt-4">
      <h3>Chat History</h3>
      {isLoggedIn ? (
        history.length ? (
          <ul className="list-group">
            {history.map((msg) => (
              <li
                key={msg.message_id}
                className={`list-group-item ${
                  msg.sender === "user" ? "text-end" : ""
                }`}
              >
                <small className="text-muted">
                  {new Date(msg.timestamp).toLocaleString()}
                </small>
                <p className="mb-0">{msg.text}</p>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-muted">No messages yet.</p>
        )
      ) : (
        <div className="alert alert-info">
          Please <strong>sign in</strong> to access your chat history.
        </div>
      )}
    </div>
  );
};

export default History;
