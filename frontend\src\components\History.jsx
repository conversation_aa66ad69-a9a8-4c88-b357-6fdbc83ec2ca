import React, { useEffect, useState } from "react";
import { fetchChatHistory } from "../../../backend/services/historyService";
import "./History.css";

const History = ({ isLoggedIn, session }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredHistory, setFilteredHistory] = useState([]);

  useEffect(() => {
    async function loadHistory() {
      if (isLoggedIn && session?.user?.id) {
        setLoading(true);
        setError(null);
        try {
          const records = await fetchChatHistory(session.user.id);
          setHistory(records);
          setFilteredHistory(records);
        } catch (error) {
          console.error("Failed to load chat history:", error);
          setError("Failed to load chat history. Please try again.");
        } finally {
          setLoading(false);
        }
      } else {
        setHistory([]);
        setFilteredHistory([]);
      }
    }
    loadHistory();
  }, [isLoggedIn, session]);

  // Filter history based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredHistory(history);
    } else {
      const filtered = history.filter(msg =>
        msg.text.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredHistory(filtered);
    }
  }, [searchTerm, history]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  return (
    <div className="history-container">
      <div className="history-header">
        <div className="header-content">
          <h2 className="history-title">
            <i className="fas fa-history"></i>
            Chat History
          </h2>
          <p className="history-subtitle">Your conversation journey with Recallo</p>
        </div>

        {isLoggedIn && history.length > 0 && (
          <div className="search-container">
            <div className="search-input-wrapper">
              <i className="fas fa-search search-icon"></i>
              <input
                type="text"
                className="search-input"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="clear-search"
                  onClick={() => setSearchTerm("")}
                  aria-label="Clear search"
                >
                  <i className="fas fa-times"></i>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="history-content">
        {!isLoggedIn ? (
          <div className="auth-prompt">
            <div className="auth-icon">
              <i className="fas fa-lock"></i>
            </div>
            <h3>Sign In Required</h3>
            <p>Please sign in to access your personalized chat history and continue your learning journey.</p>
            <div className="auth-features">
              <div className="feature-item">
                <i className="fas fa-save"></i>
                <span>Save conversations</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-search"></i>
                <span>Search history</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-sync"></i>
                <span>Sync across devices</span>
              </div>
            </div>
          </div>
        ) : loading ? (
          <div className="loading-container">
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
            <p className="loading-text">Loading your conversations...</p>
          </div>
        ) : error ? (
          <div className="error-container">
            <div className="error-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <h3>Oops! Something went wrong</h3>
            <p>{error}</p>
            <button
              className="retry-button"
              onClick={() => window.location.reload()}
            >
              <i className="fas fa-redo"></i>
              Try Again
            </button>
          </div>
        ) : filteredHistory.length === 0 ? (
          searchTerm ? (
            <div className="no-results">
              <div className="no-results-icon">
                <i className="fas fa-search"></i>
              </div>
              <h3>No matches found</h3>
              <p>No conversations match "{searchTerm}". Try a different search term.</p>
              <button
                className="clear-search-button"
                onClick={() => setSearchTerm("")}
              >
                Clear Search
              </button>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-icon">
                <i className="fas fa-comments"></i>
              </div>
              <h3>No conversations yet</h3>
              <p>Start chatting with Recallo to build your learning history!</p>
              <div className="empty-features">
                <div className="feature-tip">
                  <i className="fas fa-lightbulb"></i>
                  <span>Ask questions about any topic</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-brain"></i>
                  <span>Get personalized learning assistance</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-chart-line"></i>
                  <span>Track your learning progress</span>
                </div>
              </div>
            </div>
          )
        ) : (
          <div className="chat-history-list">
            <div className="results-info">
              {searchTerm && (
                <p className="search-results">
                  Found {filteredHistory.length} message{filteredHistory.length !== 1 ? 's' : ''}
                  matching "{searchTerm}"
                </p>
              )}
            </div>

            {filteredHistory.map((msg, index) => (
              <div
                key={msg.id}
                className={`message-bubble ${msg.sender === "user" ? "user-message" : "bot-message"}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="message-header">
                  <div className="sender-info">
                    <div className={`avatar ${msg.sender === "user" ? "user-avatar" : "bot-avatar"}`}>
                      {msg.sender === "user" ? (
                        <i className="fas fa-user"></i>
                      ) : (
                        <i className="fas fa-robot"></i>
                      )}
                    </div>
                    <span className="sender-name">
                      {msg.sender === "user" ? "You" : "Recallo"}
                    </span>
                  </div>
                  <span className="message-time">
                    {formatTime(msg.timestamp)}
                  </span>
                </div>

                <div className="message-content">
                  <p>{msg.text}</p>
                </div>

                {searchTerm && msg.text.toLowerCase().includes(searchTerm.toLowerCase()) && (
                  <div className="search-highlight-indicator">
                    <i className="fas fa-search"></i>
                    <span>Match found</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
