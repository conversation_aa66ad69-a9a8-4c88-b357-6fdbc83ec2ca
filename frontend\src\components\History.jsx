import React, { useEffect, useState } from "react";
import { fetchChatHistory } from "../../../backend/services/historyService";

const History = ({ isLoggedIn, session }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    async function loadHistory() {
      if (isLoggedIn && session?.user?.id) {
        setLoading(true);
        try {
          const records = await fetchChatHistory(session.user.id);
          setHistory(records);
        } catch (error) {
          console.error("Failed to load chat history:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setHistory([]);
      }
    }
    loadHistory();
  }, [isLoggedIn, session]);

  return (
    <div className="history container mt-4">
      <h3>Chat History</h3>
      {isLoggedIn ? (
        loading ? (
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : history.length ? (
          <div className="chat-history-container" style={{ maxHeight: "70vh", overflowY: "auto" }}>
            {history.map((msg) => (
              <div
                key={msg.id}
                className={`mb-3 p-3 rounded ${
                  msg.sender === "user"
                    ? "bg-primary text-white ms-auto"
                    : "bg-light text-dark"
                }`}
                style={{
                  maxWidth: "85%",
                  marginLeft: msg.sender === "user" ? "auto" : "0",
                  marginRight: msg.sender === "user" ? "0" : "auto"
                }}
              >
                <div className="d-flex justify-content-between align-items-start mb-2">
                  <strong className="text-capitalize">
                    {msg.sender === "user" ? "You" : "Recallo"}
                  </strong>
                  <small className={msg.sender === "user" ? "text-light" : "text-muted"}>
                    {new Date(msg.timestamp).toLocaleString()}
                  </small>
                </div>
                <p className="mb-0" style={{ whiteSpace: "pre-wrap" }}>
                  {msg.text}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-muted">
            <p>No chat history yet.</p>
            <p>Start a conversation to see your history here!</p>
          </div>
        )
      ) : (
        <div className="alert alert-info">
          Please <strong>sign in</strong> to access your chat history.
        </div>
      )}
    </div>
  );
};

export default History;
