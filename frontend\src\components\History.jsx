import React, { useEffect, useState } from "react";
import { fetchChatHistory } from "../../../backend/services/historyService";
import "./HistorySidebar.css";

const History = ({ isLoggedIn, session }) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadHistory() {
      if (isLoggedIn && session?.user?.id) {
        setLoading(true);
        setError(null);
        try {
          const history = await fetchChatHistory(session.user.id);
          console.log('Fetched history:', history);

          // Group messages into conversations for ChatGPT-style display
          const groupedConversations = groupMessagesIntoConversations(history);
          setConversations(groupedConversations);
        } catch (error) {
          console.error("Failed to load chat history:", error);
          setError("Failed to load chat history. Please try again.");
        } finally {
          setLoading(false);
        }
      } else {
        setConversations([]);
      }
    }
    loadHistory();
  }, [isLoggedIn, session]);

  // Simple grouping function for ChatGPT-style conversations
  const groupMessagesIntoConversations = (messages) => {
    if (!messages || messages.length === 0) return [];

    const conversations = [];
    let currentConversation = null;

    // Sort messages by timestamp
    const sortedMessages = [...messages].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    for (let i = 0; i < sortedMessages.length; i++) {
      const message = sortedMessages[i];

      // Start a new conversation if this is a user message and we don't have a current conversation
      // or if there's a significant time gap (more than 1 hour)
      if (message.sender === 'user') {
        const timeDiff = currentConversation ?
          (new Date(message.timestamp) - new Date(currentConversation.lastActivity)) / (1000 * 60 * 60) :
          Infinity;

        if (!currentConversation || timeDiff > 1) {
          // Save previous conversation if exists
          if (currentConversation) {
            conversations.push(currentConversation);
          }

          // Start new conversation with a meaningful title
          const title = generateConversationTitle(message.text);
          currentConversation = {
            id: `conversation_${message.id}`,
            title: title,
            startTime: message.timestamp,
            lastActivity: message.timestamp,
            messages: [message],
            messageCount: 1
          };
        } else {
          // Add to current conversation
          currentConversation.messages.push(message);
          currentConversation.lastActivity = message.timestamp;
          currentConversation.messageCount++;
        }
      } else if (currentConversation) {
        // Add bot message to current conversation
        currentConversation.messages.push(message);
        currentConversation.lastActivity = message.timestamp;
        currentConversation.messageCount++;
      }
    }

    // Add the last conversation
    if (currentConversation) {
      conversations.push(currentConversation);
    }

    return conversations.reverse(); // Most recent first
  };

  // Generate meaningful conversation titles like ChatGPT
  const generateConversationTitle = (firstMessage) => {
    // Remove common question words and get the main topic
    const cleanMessage = firstMessage
      .replace(/^(what|how|why|when|where|who|can|could|would|should|is|are|do|does|did|tell|explain|help)\s+/i, '')
      .replace(/\?+$/, '')
      .trim();

    // Take first 40 characters and add ellipsis if needed
    if (cleanMessage.length > 40) {
      return cleanMessage.substring(0, 40).trim() + '...';
    }

    return cleanMessage || 'New Conversation';
  };


  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="chatgpt-history-sidebar">
      {/* Header */}
      <div className="history-header">
        <h3 className="history-title">Chat History</h3>
      </div>

      {/* Content */}
      <div className="history-content">
        {!isLoggedIn ? (
          <div className="auth-message">
            <p>Sign in to see your chat history</p>
          </div>
        ) : loading ? (
          <div className="loading-message">
            <p>Loading...</p>
          </div>
        ) : error ? (
          <div className="error-message">
            <p>Failed to load history</p>
          </div>
        ) : conversations.length === 0 ? (
          <div className="empty-message">
            <p>No conversations yet</p>
          </div>
        ) : (
          <div className="conversations-list">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className="conversation-item"
                title={conversation.title}
              >
                <div className="conversation-icon">
                  <i className="fas fa-message"></i>
                </div>
                <div className="conversation-details">
                  <div className="conversation-title">{conversation.title}</div>
                  <div className="conversation-time">{formatTime(conversation.lastActivity)}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
