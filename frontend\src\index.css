@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap');

body {
  font-family: 'Raleway', sans-serif;
}

* {
  margin: 0;
  padding: 0;
}

:root {
  --font-stack:'Raleway', sans-serif;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --cs-primary: #edb437;
  --cs-black: #060606;
  --cs-white:#fff;
  --cs-hover:#e49c00;
  --cs-border:#ffffff21;
}

h1,
h2,
h3,
h4,
h5,
h6,
ul,
li,
a,
p {
  margin: 0;
  padding: 0;
  list-style: none;
  text-decoration: none;
}

h1 {
  font-size: 70px!important;
  font-weight: 800;
}

h2 {
  font-size: 48px;
  font-weight: 700;
  color: var(--cs-white);
}

h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--cs-white);
}

p {
  font-size: 16px;
  font-weight: 400;
  color: var(--cs-white);
}

.btn-cs, .auth-card button[type=submit] {
  background-color: var(--cs-primary);
  padding: 10px 30px;
  color: var(--cs-black);
  font-weight: 500;
  border-radius: 10px;
  border: none;
}

.btn-cs:hover, .auth-card button[type=submit]:hover {
  background-color: #e49c00;
  color: var(--cs-black);
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}

/* scrollbar */
::-webkit-scrollbar {
  width: 2px;
  border-radius: 50px;
}

::-webkit-scrollbar-track {
  background:#ffffff05;
  border-radius: 50px;
}

::-webkit-scrollbar-thumb {
  background: var(--cs-primary);
  border-radius: 50px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--cs-hover);
}

/* custom css */
/* header */
.header {
  width: 100%;
  padding: 1rem 2rem;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

.logo {
  width: 150px;
}

.nav-item a {
  color: #fff;
}

.nav-item a:hover {
  color: var(--cs-primary);
}

.navbar-nav .nav-link.active {
  color: var(--cs-primary);
}

/* header ends */

/* home */
.home-container {
  background:
    url('./assets/hero_bg.png') center / cover no-repeat, center / cover no-repeat,
    radial-gradient(circle at bottom, #EDB437 -90%, #060606 80%);
  background-blend-mode: overlay;
  background-repeat: no-repeat;
  background-position: center center;
}

.hero .row{
height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.visual_img {
  width: 120px;
  padding-bottom: 30px;
}

.home-text {
  font-size: 4rem;
  font-weight: bold;
  color: #2c3e50;
}

.hero_content h1 {
  width: 80%;
  margin: auto;
}

.grad_text {
  background: linear-gradient(to right,
      #e49c00,
      #fff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  background-size: 100% auto;
  font-size: 30px;
}

.hero_content p {
  width: 70%;
  margin: auto;
}

/* home ends */

/* chat starts */
.chat {
  height: 100vh;
  overflow: hidden;
  background:
    url('./assets/hero_bg.png') center / cover no-repeat, center / cover no-repeat,
    radial-gradient(circle at bottom, #EDB437 -90%, #060606 80%);
  background-blend-mode: overlay;
  background-repeat: no-repeat;
  background-position: center center;
}

/* ChatGPT-style Layout */
.chat-app {
  height: 100vh;
  overflow: hidden;
  background:
    url('./assets/hero_bg.png') center / cover no-repeat, center / cover no-repeat,
    radial-gradient(circle at bottom, #EDB437 -90%, #060606 80%);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.chatgpt-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Sidebar */
.left-sidebar {
  width: 280px;
  flex-shrink: 0;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  border-right: 1px solid var(--cs-border);
  overflow-y: auto;
}

/* Main Chat Area */
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Top Bar */
.chat-top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(23, 23, 23, 0.8);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--cs-border);
  flex-shrink: 0;
}

.history-toggle-btn {
  background: none;
  border: 1px solid var(--cs-border);
  color: var(--cs-white);
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.history-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--cs-primary);
}

.chat-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--cs-white);
  text-align: center;
  flex: 1;
}

.new-chat-btn {
  background: var(--cs-primary);
  border: none;
  color: var(--cs-black);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.new-chat-btn:hover {
  background: var(--cs-hover);
}

/* Chat Content */
.chat-content {
  flex: 1;
  overflow: hidden;
}

/* History Sidebar */
.history-sidebar {
  width: 320px;
  flex-shrink: 0;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  border-left: 1px solid var(--cs-border);
  overflow-y: auto;
}

/* Mobile History Overlay */
.mobile-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
}

.mobile-history-sidebar {
  width: 80%;
  max-width: 320px;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  height: 100%;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatgpt-layout {
    flex-direction: column;
  }

  .left-sidebar {
    display: none;
  }

  .chat-top-bar {
    padding: 1rem;
  }

  .chat-title {
    font-size: 1.1rem;
  }

  .new-chat-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .history-sidebar {
    display: none;
  }
}


/* ChatInterface */
.chatinterface {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
}

.chatinterface textarea {
  width: 100%;
  font-size: 1rem;
  background-color: transparent;
  border: 1px solid var(--cs-border);
  border-radius: 10px;
  color: var(--cs-white);
  margin: 0 15px;
  padding: 10px;
  font-size: 15px;
}

textarea::-webkit-input-placeholder {
  color: #fff;
}

.chatinterface textarea:focus-visible {
  outline: 1px solid var(--cs-hover);
}

.chat-input-section {
  background-color: #171717;
  padding: 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
}

.chat_ic {
  border: 1px solid var(--cs-border);
  width: 45px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chat_ic:hover {
  background-color: var(--cs-hover);
  border-color: var(--cs-hover);
}

.chat-response {
  padding: 1rem;
  background-color: #171717cc;
  border-radius: 20px;
  margin: 20px 0;
  width: 70%;
  backdrop-filter: blur(6px);
}

.chat-response-section {
  scroll-behavior: smooth;
}

.chat-response.user {
  margin-left: auto;
}

.message-actions button {
  width: 30px;
  height: 30px;
  border-radius: 10px;
}

.rec_img {
  width: 40px;
  padding: 0;
  padding-right: 10px;
}


.ai *{
  margin-bottom: 10px;
  color: var(--cs-white);
  font-size: 16px;
}

.processing-spinner {
  font-style: italic;
  color: gray;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* signin page */
.auth-card{
  font-family: var(--font-stack)!important;
}
