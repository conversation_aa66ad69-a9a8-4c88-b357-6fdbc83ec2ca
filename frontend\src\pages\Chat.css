/* Modern Responsive Chat Layout */
.chat-app {
  height: 100vh;
  overflow: hidden;
  background: #f8fafc;
}

/* Mobile Navigation */
.mobile-nav {
  display: flex;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  margin-right: 0.5rem;
}

.nav-btn:last-child {
  margin-right: 0;
}

.nav-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-1px);
}

.nav-btn i {
  font-size: 1.1rem;
}

.sidebar-toggle {
  background: #f1f5f9;
  border: none;
  padding: 0.75rem;
  border-radius: 12px;
  color: #64748b;
  margin-left: 0.5rem;
  transition: all 0.2s ease;
  min-width: 48px;
}

.sidebar-toggle:hover {
  background: #e2e8f0;
  color: #334155;
}

/* Desktop Layout */
.desktop-layout {
  height: 100vh;
  width: 100%;
}

.sidebar-section {
  width: 280px;
  flex-shrink: 0;
  background: white;
  border-right: 1px solid #e2e8f0;
  overflow-y: auto;
}

.chat-section {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.history-section {
  width: 400px;
  flex-shrink: 0;
  background: #f8fafc;
  border-left: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Mobile Layout */
.mobile-layout {
  height: 100vh;
  padding-top: 80px; /* Account for mobile nav */
  overflow: hidden;
}

.mobile-chat,
.mobile-history {
  height: 100%;
  overflow: hidden;
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  animation: fadeIn 0.3s ease;
}

.mobile-sidebar {
  width: 280px;
  background: white;
  height: 100%;
  overflow-y: auto;
  transform: translateX(-100%);
  animation: slideIn 0.3s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* History Component Mobile Overrides */
@media (max-width: 767.98px) {
  .mobile-history .history-container {
    height: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .mobile-history .history-header {
    flex-shrink: 0;
    margin-bottom: 1rem;
  }
  
  .mobile-history .history-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .mobile-history .chat-sessions-list {
    flex: 1;
    margin: 0;
    max-height: none;
    height: 100%;
    overflow-y: auto;
    padding: 1rem;
  }
  
  .mobile-history .session-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }
  
  .mobile-history .session-header {
    gap: 0.75rem;
  }
  
  .mobile-history .session-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .mobile-history .session-title {
    font-size: 1rem;
    line-height: 1.3;
  }
  
  .mobile-history .session-preview {
    font-size: 0.9rem;
    line-height: 1.4;
  }
}

/* Tablet Adjustments */
@media (min-width: 768px) and (max-width: 1199.98px) {
  .history-section {
    width: 350px;
  }
  
  .sidebar-section {
    width: 250px;
  }
}

/* Large Desktop */
@media (min-width: 1400px) {
  .history-section {
    width: 450px;
  }
  
  .sidebar-section {
    width: 320px;
  }
}

/* Smooth transitions */
.chat-section,
.history-section,
.sidebar-section {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.nav-btn:focus,
.sidebar-toggle:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Loading states */
.mobile-layout.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Landscape mobile optimization */
@media (max-width: 767.98px) and (orientation: landscape) {
  .mobile-nav {
    padding: 0.5rem 1rem;
  }
  
  .nav-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .nav-btn i {
    font-size: 1rem;
  }
  
  .mobile-layout {
    padding-top: 70px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-nav {
    border-bottom-width: 0.5px;
  }
  
  .sidebar-section,
  .history-section {
    border-width: 0.5px;
  }
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
  .chat-app {
    background: #1a202c;
  }
  
  .mobile-nav {
    background: #2d3748;
    border-bottom-color: #4a5568;
  }
  
  .nav-btn {
    color: #a0aec0;
  }
  
  .sidebar-toggle {
    background: #4a5568;
    color: #a0aec0;
  }
  
  .desktop-layout .sidebar-section,
  .desktop-layout .chat-section {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .desktop-layout .history-section {
    background: #1a202c;
    border-color: #4a5568;
  }
}

/* Print styles */
@media print {
  .mobile-nav,
  .sidebar-section,
  .history-section {
    display: none !important;
  }
  
  .chat-section {
    width: 100% !important;
  }
}
