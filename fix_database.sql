-- CRITICAL FIX: Add user_id column to chat_logs table
-- Run this in your Supabase SQL Editor

-- Add user_id column to chat_logs table to associate chat logs with users
ALTER TABLE public.chat_logs 
ADD COLUMN user_id UUID REFERENCES users(user_id) ON DELETE CASCADE;

-- Create index for better query performance
CREATE INDEX idx_chat_logs_user_id ON public.chat_logs(user_id);
CREATE INDEX idx_chat_logs_created_at ON public.chat_logs(created_at);

-- Verify the change
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chat_logs' AND table_schema = 'public';
