-- CRITICAL FIX: Add user_id column to chat_logs table
-- Run this in your Supabase SQL Editor

-- Add user_id column to chat_logs table to associate chat logs with users
ALTER TABLE public.chat_logs
ADD COLUMN user_id UUID REFERENCES users(user_id) ON DELETE CASCADE;

-- Create index for better query performance
CREATE INDEX idx_chat_logs_user_id ON public.chat_logs(user_id);
CREATE INDEX idx_chat_logs_created_at ON public.chat_logs(created_at);

-- Insert a test user for testing (with dummy password hash)
INSERT INTO users (user_id, email, name, password_hash)
VALUES ('a0929f2e-35a0-4544-aac0-f406d17f77d7', '<EMAIL>', 'Test User', 'dummy_hash_for_testing')
ON CONFLICT (user_id) DO NOTHING;

-- Verify the changes
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'chat_logs' AND table_schema = 'public';

-- Verify the test user was created
SELECT user_id, email, name FROM users WHERE user_id = 'a0929f2e-35a0-4544-aac0-f406d17f77d7';
