/**
 * Fetches chat history for a given user from the backend API.
 * @param {string} userId
 * @returns {Promise<Array<{ id: string, sender: string, text: string, timestamp: string }>>}
 */
export async function fetchChatHistory(userId) {
  try {
    const response = await fetch(`http://127.0.0.1:5000/history?user_id=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.history || [];
  } catch (error) {
    console.error("Error fetching chat history:", error.message);
    return [];
  }
}
