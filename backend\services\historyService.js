import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

/**
 * Fetches chat history for a given user.
 * @param {string} userId
 * @returns {Promise<Array<{ message_id: string, sender: string, text: string, timestamp: string }>>}
 */
export async function fetchChatHistory(userId) {
  const { data, error } = await supabase
    .from("chat_messages")
    .select("message_id, sender, text, timestamp")
    .eq("user_id", userId)
    .order("timestamp", { ascending: true });

  if (error) {
    console.error("Error fetching chat history:", error.message);
    return [];
  }
  return data;
}
