/* History Component Styles */
.history-container {
  height: 100vh;
  width: 100%;
  background: url('../assets/hero_bg.png') center / cover no-repeat,
              radial-gradient(circle at bottom, #EDB437 -90%, #060606 80%);
  background-blend-mode: overlay;
  background-repeat: no-repeat;
  background-position: center center;
  padding: 1rem;
  font-family: 'Raleway', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* Header Styles */
.history-header {
  flex-shrink: 0;
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.header-content {
  margin-bottom: 1.5rem;
}

.history-title {
  color: var(--cs-white);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.history-title i {
  font-size: 1.8rem;
  opacity: 1;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  color: var(--cs-primary);
}

.history-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Search Container */
.search-container {
  margin: 0 auto;
  max-width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 0.875rem 0.875rem 0.875rem 2.75rem;
  border: 1px solid var(--cs-border);
  border-radius: 25px;
  background: rgba(23, 23, 23, 0.8);
  backdrop-filter: blur(10px);
  font-size: 0.95rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  color: var(--cs-white);
}

.search-input:focus {
  outline: none;
  background: rgba(23, 23, 23, 0.9);
  box-shadow: 0 12px 40px rgba(237, 180, 55, 0.2);
  transform: translateY(-1px);
  border-color: var(--cs-primary);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--cs-primary);
  z-index: 2;
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--cs-primary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: rgba(237, 180, 55, 0.2);
  color: var(--cs-hover);
}

/* Content Container */
.history-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

/* Auth Prompt */
.auth-prompt {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.auth-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1.5rem;
}

.auth-prompt h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.auth-prompt p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.auth-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
}

.feature-item i {
  font-size: 1.2rem;
}

/* Loading Styles */
.loading-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-left: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Error Styles */
.error-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 3rem;
  color: #e74c3c;
  margin-bottom: 1.5rem;
}

.error-container h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.error-container p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Empty State */
.empty-state, .no-results {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.empty-icon, .no-results-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1.5rem;
}

.empty-state h3, .no-results h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.empty-state p, .no-results p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.feature-tip {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #667eea;
  font-weight: 500;
  font-size: 1rem;
}

.feature-tip i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.clear-search-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Chat Sessions List */
.chat-sessions-list {
  background: rgba(23, 23, 23, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  flex: 1;
  overflow-y: auto;
  margin: 0;
  border: 1px solid var(--cs-border);
}

.results-info {
  margin-bottom: 1.5rem;
  text-align: center;
}

.search-results {
  color: #667eea;
  font-weight: 500;
  margin: 0;
  font-size: 0.95rem;
}

/* Session Cards */
.session-card {
  background: white;
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.session-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 50px rgba(237, 180, 55, 0.3);
  border-color: var(--cs-primary);
}

.session-card:active {
  transform: translateY(-3px);
}

.session-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.session-icon {
  width: 48px;
  height: 48px;
  background: var(--cs-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cs-black);
  font-size: 1.2rem;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(237, 180, 55, 0.4);
  position: relative;
  font-weight: 600;
}

.session-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 12px;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.session-preview {
  font-size: 0.95rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
  word-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.session-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  flex-shrink: 0;
}

.session-time {
  font-size: 0.85rem;
  color: #999;
  font-weight: 400;
}

.message-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--cs-black);
  background: var(--cs-primary);
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-weight: 600;
  border: 1px solid rgba(237, 180, 55, 0.3);
  box-shadow: 0 2px 8px rgba(237, 180, 55, 0.2);
}

.message-count i {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Message Bubbles */
.message-bubble {
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  margin-left: auto;
  margin-right: 0;
  max-width: 85%;
}

.bot-message {
  margin-left: 0;
  margin-right: auto;
  max-width: 85%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: white;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bot-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.sender-name {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.message-time {
  color: #999;
  font-size: 0.85rem;
  font-weight: 400;
}

.message-content {
  background: white;
  padding: 1.25rem;
  border-radius: 18px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.user-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-message .message-content::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #667eea;
}

.bot-message .message-content::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
}

.message-content p {
  margin: 0;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.search-highlight-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  color: #667eea;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-container {
    padding: 1rem;
    height: 100vh;
  }

  .history-header {
    margin-bottom: 1rem;
  }

  .history-title {
    font-size: 1.75rem;
    gap: 0.5rem;
  }

  .history-title i {
    font-size: 1.5rem;
  }

  .history-subtitle {
    font-size: 0.95rem;
  }

  .search-input {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 0.9rem;
    border-radius: 20px;
  }

  .search-icon {
    left: 0.75rem;
    font-size: 0.9rem;
  }

  .clear-search {
    right: 0.75rem;
  }

  .auth-prompt, .loading-container, .error-container, .empty-state, .no-results {
    padding: 1.5rem 1rem;
    border-radius: 16px;
  }

  .auth-features {
    flex-direction: column;
    gap: 0.75rem;
  }

  .chat-sessions-list {
    padding: 1rem;
    border-radius: 16px;
    flex: 1;
    overflow-y: auto;
  }

  .session-card {
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 0.75rem;
  }

  .session-header {
    gap: 0.75rem;
  }

  .session-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    border-radius: 10px;
  }

  .session-title {
    font-size: 0.95rem;
    line-height: 1.3;
  }

  .session-preview {
    font-size: 0.85rem;
    line-height: 1.4;
    -webkit-line-clamp: 2;
  }

  .session-meta {
    align-items: flex-start;
    gap: 0.5rem;
  }

  .session-time {
    font-size: 0.75rem;
  }

  .message-count {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
  }

  .session-card:hover {
    transform: translateY(-2px);
  }
}

@media (max-width: 480px) {
  .history-title {
    font-size: 1.75rem;
  }
  
  .auth-prompt h3, .error-container h3, .empty-state h3, .no-results h3 {
    font-size: 1.5rem;
  }
  
  .auth-prompt p, .error-container p, .empty-state p, .no-results p {
    font-size: 1rem;
  }
  
  .feature-tip {
    font-size: 0.95rem;
  }
}

/* Smooth scrolling for chat sessions */
.chat-sessions-list {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.chat-sessions-list::-webkit-scrollbar {
  width: 6px;
}

.chat-sessions-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-sessions-list::-webkit-scrollbar-thumb {
  background: var(--cs-primary);
  border-radius: 3px;
}

.chat-sessions-list::-webkit-scrollbar-thumb:hover {
  background: var(--cs-hover);
}

/* Session card hover effects */
.session-card {
  position: relative;
  overflow: hidden;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(237, 180, 55, 0.2), transparent);
  transition: left 0.5s ease;
}

.session-card:hover::before {
  left: 100%;
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .history-container {
    padding: 0.75rem;
  }

  .history-title {
    font-size: 1.5rem;
  }

  .history-title i {
    font-size: 1.3rem;
  }

  .history-subtitle {
    font-size: 0.9rem;
  }

  .search-input {
    padding: 0.625rem 0.625rem 0.625rem 2.25rem;
    font-size: 0.85rem;
  }

  .search-icon {
    left: 0.625rem;
    font-size: 0.85rem;
  }

  .clear-search {
    right: 0.625rem;
  }

  .chat-sessions-list {
    padding: 0.75rem;
  }

  .session-card {
    padding: 0.875rem;
    margin-bottom: 0.625rem;
  }

  .session-header {
    gap: 0.625rem;
  }

  .session-icon {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
    border-radius: 8px;
  }

  .session-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    line-height: 1.25;
  }

  .session-preview {
    font-size: 0.8rem;
    line-height: 1.35;
    -webkit-line-clamp: 2;
  }

  .session-meta {
    gap: 0.375rem;
  }

  .session-time {
    font-size: 0.7rem;
  }

  .message-count {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
  }

  .auth-prompt, .loading-container, .error-container, .empty-state, .no-results {
    padding: 1.25rem 0.875rem;
  }

  .empty-features {
    gap: 0.5rem;
  }

  .feature-tip {
    padding: 0.75rem;
    font-size: 0.85rem;
  }
}
