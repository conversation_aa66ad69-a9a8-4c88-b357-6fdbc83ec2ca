/* ChatGPT-style History Sidebar */
.chatgpt-history-sidebar {
  height: 100%;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  color: var(--cs-white);
  border-right: 1px solid var(--cs-border);
}

/* Header */
.history-header {
  padding: 1rem;
  border-bottom: 1px solid var(--cs-border);
  flex-shrink: 0;
}

.history-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--cs-white);
  margin: 0;
  text-align: center;
}

/* Content */
.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

/* Messages */
.auth-message,
.loading-message,
.error-message,
.empty-message {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--cs-text-muted);
  font-size: 0.9rem;
}

/* Conversations List */
.conversations-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* Conversation Item - ChatGPT Style */
.conversation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid transparent;
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--cs-border);
}

.conversation-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cs-text-muted);
  font-size: 0.9rem;
  flex-shrink: 0;
}

.conversation-details {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-size: 0.85rem;
  font-weight: 400;
  color: var(--cs-white);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--cs-text-muted);
  display: block;
}

/* Scrollbar Styling */
.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: transparent;
}

.history-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatgpt-history-sidebar {
    border-right: none;
  }

  .history-header {
    padding: 0.75rem;
  }

  .history-title {
    font-size: 1rem;
  }

  .conversation-item {
    padding: 0.625rem;
  }

  .conversation-icon {
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
  }

  .conversation-title {
    font-size: 0.8rem;
  }

  .conversation-time {
    font-size: 0.7rem;
  }
}
